{"name": "isbinaryfile", "description": "Detects if a file is binary in Node.js. Similar to Perl's -B.", "version": "4.0.10", "keywords": ["text", "binary", "encoding", "istext", "is text", "isbinary", "is binary", "is text or binary", "is text or binary file", "isbinaryfile", "is binary file", "istextfile", "is text file"], "devDependencies": {"@types/jest": "^23.3.14", "@types/node": "^10.17.59", "jest": "^26.5.5", "prettier": "^1.19.1", "release-it": "^14.13.1", "ts-jest": "^26.5.5", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "typescript": "^3.9.9"}, "engines": {"node": ">= 8.0.0"}, "files": ["lib/**/*"], "license": "MIT", "main": "lib/index.js", "types": "lib/index.d.ts", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/gjtorikian/", "repository": {"type": "git", "url": "https://github.com/gjtorikian/isBinaryFile"}, "scripts": {"build": "tsc", "format": "prettier --write \"src/**/*.ts\" \"src/**/*.js\" && tslint --fix -c tslint.json 'src/**/*.ts'", "lint": "tslint -p tsconfig.json", "prepare": "npm run build", "release": "release-it", "prepublishOnly": "npm test && npm run lint", "preversion": "npm run lint", "version": "npm run format && git add -A src", "postversion": "git push && git push --tags", "test": "jest --config jestconfig.json", "watch": "tsc -w"}}