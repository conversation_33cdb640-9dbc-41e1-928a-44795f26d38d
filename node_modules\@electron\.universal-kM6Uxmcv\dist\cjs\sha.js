"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sha = void 0;
const fs = __importStar(require("fs-extra"));
const crypto = __importStar(require("crypto"));
const debug_1 = require("./debug");
exports.sha = async (filePath) => {
    debug_1.d('hashing', filePath);
    const hash = crypto.createHash('sha256');
    hash.setEncoding('hex');
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(hash);
    await new Promise((resolve, reject) => {
        fileStream.on('end', () => resolve());
        fileStream.on('error', (err) => reject(err));
    });
    return hash.read();
};
//# sourceMappingURL=sha.js.map