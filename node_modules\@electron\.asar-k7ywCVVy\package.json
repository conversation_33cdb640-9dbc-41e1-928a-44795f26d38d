{"name": "@electron/asar", "description": "Creating Electron app packages", "version": "3.4.1", "main": "./lib/asar.js", "types": "./lib/asar.d.ts", "bin": {"asar": "./bin/asar.js"}, "files": ["bin", "lib"], "engines": {"node": ">=10.12.0"}, "license": "MIT", "homepage": "https://github.com/electron/asar", "repository": {"type": "git", "url": "git+https://github.com/electron/asar.git"}, "bugs": {"url": "https://github.com/electron/asar/issues"}, "publishConfig": {"provenance": true}, "scripts": {"build": "tsc", "mocha": "xvfb-maybe electron-mocha && mocha", "mocha:update": "mocha --update", "mocha:watch": "mocha --watch", "test": "yarn lint && yarn mocha", "lint": "yarn prettier:check", "prettier": "prettier \"src/**/*.ts\" \"test/**/*.js\" \"*.js\"", "prettier:check": "yarn prettier --check", "prettier:write": "yarn prettier --write", "prepare": "tsc"}, "dependencies": {"commander": "^5.0.0", "glob": "^7.1.6", "minimatch": "^3.0.4"}, "devDependencies": {"@types/minimatch": "^3.0.5", "@types/node": "^12.0.0", "chai": "^4.5.0", "electron": "^22.0.0", "electron-mocha": "^13.0.1", "lodash": "^4.17.15", "mocha": "^10.1.0", "mocha-chai-jest-snapshot": "^1.1.6", "prettier": "^3.3.3", "rimraf": "^3.0.2", "typescript": "^5.5.4", "xvfb-maybe": "^0.2.1"}}